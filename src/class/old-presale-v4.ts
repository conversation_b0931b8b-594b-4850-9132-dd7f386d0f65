import {
  Address,
  Chain,
  Client,
  encodeAbiParameters,
  erc20Abi,
  getContract,
  keccak256,
  WalletClient,
} from 'viem';

import { membershipV4Abi } from '@/abi/membership/membership-v4-abi';
import { presaleV4Abi } from '@/abi/presale/presale-v5-abi';

import {
  IPresale,
  Membership,
  Presale,
  PresaleRoundState,
  PresaleRoundStateValue,
  Round,
} from './interface/presale';
import { PresaleVersion } from './presale-factory';

type GetRoundsReturnType = [number[], Round[], PresaleRoundStateValue[]];

interface PresaleV4Props {
  client: Client;
  contractAddress: Address;
  vestedToken: Address;
  collectedToken: Address;
  listingTimestamp: bigint;
  claimbackPeriod: bigint;
  rounds: Round[];
  membership: Address;
}

interface PresaleV4Proofs {
  address: Address;
  price: string;
  allocation: string;
  claimbackPeriod: number;
  tradeable: number;
  tgeNumerator: number;
  tgeDenominator: number;
  cliffDuration: number;
  cliffNumerator: number;
  cliffDenominator: number;
  vestingPeriodCount: number;
  vestingPeriodDuration: number;
  proofs: string[];
}

interface PresaleV4Membership extends Membership {
  tradeable: 1 | 2;
  claimbackPeriod: number;
}

/**
 * OldPresaleV4 - Modified version of PresaleV4 for old contract migrations
 * 
 * This class is identical to PresaleV4 except it fetches proofs from the "old" directory:
 * `/proofs/{round}/old/{tier}/{address}.json` instead of `/proofs/{round}/{tier}/{address}.json`
 * 
 * This ensures that when fetching memberships for migration purposes, we get the correct
 * proof data from the old contract structure.
 */
export class OldPresaleV4 implements IPresale {
  public getVersion(): PresaleVersion {
    return 'v4';
  }

  private client: Client;
  private presaleContractAddress: Address;
  private vestedToken: Address;
  private collectedToken: Address;
  private listingTimestamp: bigint;
  private claimbackPeriod: bigint;
  private rounds: Round[];
  private membership: Address;

  constructor({
    client,
    contractAddress,
    vestedToken,
    collectedToken,
    listingTimestamp,
    claimbackPeriod,
    rounds,
    membership,
  }: PresaleV4Props) {
    this.client = client;
    this.presaleContractAddress = contractAddress;
    this.vestedToken = vestedToken;
    this.collectedToken = collectedToken;
    this.listingTimestamp = listingTimestamp;
    this.claimbackPeriod = claimbackPeriod;
    this.rounds = rounds;
    this.membership = membership;
  }

  public static async createInstance(
    publicClient: Client,
    presaleContractAddress: Address,
  ): Promise<IPresale> {
    const presaleContract = getContract({
      client: publicClient,
      address: presaleContractAddress,
      abi: presaleV4Abi,
    });

    const [
      vestedToken,
      collectedToken,
      rawRoundsData,
      membership,
      listingTimestamp,
      claimbackPeriod,
    ] = await Promise.all([
      presaleContract.read.tokenA() as Promise<Address>,
      presaleContract.read.tokenB() as Promise<Address>,
      presaleContract.read.getRounds() as unknown as Promise<GetRoundsReturnType>,
      presaleContract.read.membership() as Promise<Address>,
      presaleContract.read.getListingTimestamp() as Promise<bigint>,
      presaleContract.read.claimbackPeriod() as Promise<bigint>,
    ]);

    const [roundIds, roundsData, roundStates] = rawRoundsData;

    const rounds: Round[] = roundIds.map((roundId, index) => ({
      roundId: Number(roundId),
      state: roundStates[index],
      name: roundsData[index].name,
      startTimestamp: Number(roundsData[index].startTimestamp),
      endTimestamp: Number(roundsData[index].endTimestamp),
      listingTimestamp: Number(roundsData[index].listingTimestamp),
      refundsEndTimestamp: Number(roundsData[index].refundsEndTimestamp),
      proofsUri: roundsData[index].proofsUri,
      whitelistRoot: roundsData[index].whitelistRoot,
    }));

    return new OldPresaleV4({
      client: publicClient,
      contractAddress: presaleContractAddress,
      vestedToken,
      collectedToken,
      listingTimestamp,
      claimbackPeriod,
      rounds,
      membership,
    });
  }

  public getPresaleData(): Presale {
    return {
      presaleContractAddress: this.presaleContractAddress,
      vestedToken: this.vestedToken,
      collectedToken: this.collectedToken,
      listingTimestamp: Number(this.listingTimestamp),
      claimbackPeriod: Number(this.claimbackPeriod),
      rounds: this.rounds,
    };
  }

  public async getMemberships(walletAddress: Address): Promise<PresaleV4Membership[]> {
    const memberships: PresaleV4Membership[] = [];

    const proofsMemberships = await this.getProofsMemberships(walletAddress);

    if (proofsMemberships.length > 0) {
      memberships.push(...proofsMemberships);
    }

    const nftMemberships = await this.getNftMemberships(walletAddress);

    if (nftMemberships.length > 0) {
      memberships.push(...nftMemberships);
    }

    return memberships;
  }

  /**
   * Modified getProofsMemberships that fetches from OLD proof paths
   * Path: /proofs/{round}/old/{tier}/{address}.json
   */
  private async getProofsMemberships(
    walletAddress: Address,
  ): Promise<PresaleV4Membership[]> {
    const activeRounds = this.rounds.filter(
      (round) => round.state !== PresaleRoundState.vesting,
    );

    // Modified getProofsForTier to use OLD proof paths
    const getProofsForTier = async (round: string, tier: number, address: Address) => {
      const proofs = fetch(
        `/proofs/${round.toLowerCase()}/old/${tier}/${address.toLowerCase()}.json`,
      )
        .then((response) => response.json())
        .catch(() => {
          // console.log(`No OLD proofs for address ${address} in round ${round} tier ${tier}`);
          return;
        });

      return proofs;
    };

    const proofsPromises = activeRounds.map((round) => {
      const tiers = Array.from({ length: 3 }, (_, i) => i + 1);
      const promises = tiers.map((tier) => {
        return getProofsForTier(round.name, tier, walletAddress);
      });

      return Promise.all(promises);
    });

    const proofs: { [key: number]: PresaleV4Proofs[] } =
      await Promise.all(proofsPromises);

    const memberships: PresaleV4Membership[] = [];

    for (const [index, round] of activeRounds.entries()) {
      for (const [tier, proof] of proofs[index].entries()) {
        if (proof) {
          const _membership: PresaleV4Membership = {
            id: `${round.name} Tier ${tier < 2 ? tier + 1 : '3-5'}`,
            roundId: round.roundId,
            usage: {
              max: '0',
              current: proof.allocation,
            },
            price: proof.price,
            allocation: proof.allocation,
            claimableBackUnit: '0',
            tgeNumerator: proof.tgeNumerator,
            tgeDenominator: proof.tgeDenominator,
            cliffDuration: proof.cliffDuration,
            cliffNumerator: proof.cliffNumerator,
            cliffDenominator: proof.cliffDenominator,
            vestingPeriodCount: proof.vestingPeriodCount,
            vestingPeriodDuration: proof.vestingPeriodDuration,
            tgeStartTimestamp: 0,
            locked: '0',
            unlocked: '0',
            nextUnlockTimestamp: 0,
            nextUnlockValue: '0',
            proofs: proof.proofs,
            tradeable: proof.tradeable as 1 | 2,
            claimbackPeriod: proof.claimbackPeriod,
          };

          memberships.push(_membership);
        }
      }
    }

    const presaleContract = getContract({
      address: this.presaleContractAddress,
      abi: presaleV4Abi,
      client: this.client,
    });

    const filtredMemberships = await Promise.all(
      memberships.map(async (membership) => {
        const proofsHash = this.getProofsHash({
          address: walletAddress,
          price: BigInt(membership.price),
          allocation: BigInt(membership.allocation),
          claimbackPeriod: BigInt(membership.claimbackPeriod),
          tgeNumerator: membership.tgeNumerator,
          tgeDenominator: membership.tgeDenominator,
          cliffDuration: membership.cliffDuration,
          cliffNumerator: membership.cliffNumerator,
          cliffDenominator: membership.cliffDenominator,
          vestingPeriodCount: membership.vestingPeriodCount,
          vestingPeriodDuration: membership.vestingPeriodDuration,
          tradeable: membership.tradeable,
        });

        return {
          participated: await presaleContract.read.roundParticipants([
            BigInt(membership.roundId),
            proofsHash,
          ]),
          membership: membership,
        };
      }),
    ).then((results) => {
      return results
        .filter((result) => !result.participated)
        .map((result) => result.membership);
    });

    return filtredMemberships;
  }

  private async getNftMemberships(walletAddress: Address) {
    const memberships: PresaleV4Membership[] = [];

    const membershipNftContract = getContract({
      address: this.membership,
      abi: membershipV4Abi,
      client: this.client,
    });

    const membershipBalance = await membershipNftContract.read.balanceOf([walletAddress]);

    if (membershipBalance > 0) {
      const promises = Array.from({ length: Number(membershipBalance) }, (_, i) => {
        return membershipNftContract.read.tokenOfOwnerByIndex([walletAddress, BigInt(i)]);
      });

      const membershipIds = await Promise.all(promises);

      const membershipPromises = membershipIds.map((membershipId) => {
        return Promise.all([
          membershipNftContract.read.getUsage([membershipId]),
          membershipNftContract.read.getAttributes([membershipId]),
          membershipNftContract.read.getRoundId([membershipId]),
          membershipNftContract.read.getStartTimestamp(),
          membershipNftContract.read.unlocked([membershipId]) as Promise<bigint>,
        ]);
      });

      const membershipData = await Promise.all(membershipPromises);

      memberships.push(
        ...membershipData.map((membership, index) => {
          const [usage, attributes, roundId, start, unlocked] = membership;

          return {
            id: membershipIds[index].toString(),
            roundId: Number(roundId),
            usage: {
              max: usage.max.toString(),
              current: usage.current.toString(),
            },
            price: attributes.price.toString(),
            allocation: attributes.allocation.toString(),
            claimableBackUnit: '0',
            tgeNumerator: attributes.tgeNumerator,
            tgeDenominator: attributes.tgeDenominator,
            cliffDuration: attributes.cliffDuration,
            cliffNumerator: attributes.cliffNumerator,
            cliffDenominator: attributes.cliffDenominator,
            vestingPeriodCount: attributes.vestingPeriodCount,
            vestingPeriodDuration: attributes.vestingPeriodDuration,
            tgeStartTimestamp: Number(start),
            locked: (BigInt(attributes.allocation) - unlocked).toString(),
            unlocked: unlocked.toString(),
            nextUnlockTimestamp: 0,
            nextUnlockValue: '0',
            tradeable: attributes.tradeable as 1 | 2,
            claimbackPeriod: Number(attributes.claimbackPeriod),
          } as PresaleV4Membership;
        }),
      );
    }

    return memberships;
  }

  private getProofsHash({
    address,
    price,
    allocation,
    claimbackPeriod,
    tgeNumerator,
    tgeDenominator,
    cliffDuration,
    cliffNumerator,
    cliffDenominator,
    vestingPeriodCount,
    vestingPeriodDuration,
    tradeable,
  }: {
    address: Address;
    price: bigint;
    allocation: bigint;
    claimbackPeriod: bigint;
    tgeNumerator: number;
    tgeDenominator: number;
    cliffDuration: number;
    cliffNumerator: number;
    cliffDenominator: number;
    vestingPeriodCount: number;
    vestingPeriodDuration: number;
    tradeable: number;
  }) {
    return keccak256(
      encodeAbiParameters(
        [
          { name: 'user', type: 'address' },
          { name: 'price', type: 'uint256' },
          { name: 'allocation', type: 'uint256' },
          { name: 'claimbackPeriod', type: 'uint256' },
          { name: 'tgeNumerator', type: 'uint256' },
          { name: 'tgeDenominator', type: 'uint256' },
          { name: 'cliffDuration', type: 'uint256' },
          { name: 'cliffNumerator', type: 'uint256' },
          { name: 'cliffDenominator', type: 'uint256' },
          { name: 'vestingPeriodCount', type: 'uint256' },
          { name: 'vestingPeriodDuration', type: 'uint256' },
          { name: 'tradeable', type: 'uint256' },
        ],
        [
          address,
          price,
          allocation,
          claimbackPeriod,
          BigInt(tgeNumerator),
          BigInt(tgeDenominator),
          BigInt(cliffDuration),
          BigInt(cliffNumerator),
          BigInt(cliffDenominator),
          BigInt(vestingPeriodCount),
          BigInt(vestingPeriodDuration),
          BigInt(tradeable),
        ],
      ),
    );
  }

  public setApproval(
    walletClient: WalletClient,
    chain: Chain,
    amount: bigint,
  ): Promise<Address> {
    if (walletClient.account === undefined) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.collectedToken,
      abi: erc20Abi,
      functionName: 'approve',
      args: [this.presaleContractAddress, amount],
      account: walletClient.account,
      chain,
    });
  }

  public buyTokens(
    walletClient: WalletClient,
    chain: Chain,
    membership: PresaleV4Membership,
    amount: bigint,
  ): Promise<Address> {
    if (walletClient.account === undefined) {
      throw new Error('Account is undefined');
    }

    if (membership.proofs) {
      return walletClient.writeContract({
        address: this.presaleContractAddress,
        abi: presaleV4Abi,
        functionName: 'buy',
        args: [
          BigInt(membership.roundId),
          amount,
          {
            price: BigInt(membership.price),
            allocation: BigInt(membership.allocation),
            claimbackPeriod: BigInt(membership.claimbackPeriod),
            tgeNumerator: Number(membership.tgeNumerator),
            tgeDenominator: Number(membership.tgeDenominator),
            cliffDuration: Number(membership.cliffDuration),
            cliffNumerator: Number(membership.cliffNumerator),
            cliffDenominator: Number(membership.cliffDenominator),
            vestingPeriodCount: Number(membership.vestingPeriodCount),
            vestingPeriodDuration: Number(membership.vestingPeriodDuration),
            tradeable: membership.tradeable,
          },
          membership.proofs! as Address[],
        ],
        account: walletClient.account,
        chain,
      });
    } else {
      return walletClient.writeContract({
        address: this.presaleContractAddress,
        abi: presaleV4Abi,
        functionName: 'extend',
        args: [BigInt(membership.id), amount],
        account: walletClient.account,
        chain,
      });
    }
  }

  public claimTokens(
    walletClient: WalletClient,
    chain: Chain,
    membershipId: string,
  ): Promise<Address> {
    if (walletClient.account === undefined) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.presaleContractAddress,
      abi: presaleV4Abi,
      functionName: 'claim',
      args: [BigInt(membershipId)],
      account: walletClient.account,
      chain,
    });
  }

  public claimBackTokens(
    walletClient: WalletClient,
    chain: Chain,
    membershipId: string,
    amount: bigint,
  ): Promise<Address> {
    if (walletClient.account === undefined) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.presaleContractAddress,
      abi: presaleV4Abi,
      functionName: 'claimback',
      args: [BigInt(membershipId), amount],
      account: walletClient.account,
      chain,
    });
  }

  public transferMembership(
    walletClient: WalletClient,
    chain: Chain,
    to: Address,
    membershipId: string,
  ): Promise<Address> {
    if (walletClient.account === undefined) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.membership,
      abi: membershipV4Abi,
      functionName: 'transferFrom',
      args: [walletClient.account.address, to, BigInt(membershipId)],
      account: walletClient.account,
      chain,
    });
  }
}
