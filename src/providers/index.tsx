import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { WagmiProvider } from 'wagmi';

import { config } from '@/lib/wagmi/config';

import { config as raiinmakerConfig } from '../../config/raiinmaker';
import { PresaleProvider } from './presale-provider';
import { ProjectConfig, ProjectConfigProvider } from './project-config-provider';
import { PublicClientProvider } from './public-client-provider';
import { ThemeProvider } from './theme-provider';

const queryClient = new QueryClient();

export default function Providers({ children }: { children: React.ReactNode }) {
  const projectConfig: ProjectConfig = raiinmakerConfig;

  const wagmiConfig = config(projectConfig.walletConnectProjectId);

  return (
    <ProjectConfigProvider projectConfig={projectConfig}>
      <WagmiProvider config={wagmiConfig}>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider>
            <PublicClientProvider>
              <PresaleProvider>{children}</PresaleProvider>
            </PublicClientProvider>
          </ThemeProvider>
        </QueryClientProvider>
      </WagmiProvider>
    </ProjectConfigProvider>
  );
}
